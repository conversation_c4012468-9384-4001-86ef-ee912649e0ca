import axios from 'axios';
import { Product, ProductsResponse, ProductFormData, ProductQueryParams } from './types';

const API_BASE_URL = 'https://dummyjson.com';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const productsApi = {
  getProducts: async (params: ProductQueryParams): Promise<ProductsResponse> => {
    const { data } = await api.get('/products', { params });
    return data;
  },

  getProduct: async (id: number): Promise<Product> => {
    const { data } = await api.get(`/products/${id}`);
    return data;
  },

  searchProducts: async (query: string, params?: Partial<ProductQueryParams>): Promise<ProductsResponse> => {
    const { data } = await api.get('/products/search', { 
      params: { q: query, ...params } 
    });
    return data;
  },

  createProduct: async (product: ProductFormData): Promise<Product> => {
    const { data } = await api.post('/products/add', product);
    return data;
  },

  updateProduct: async (id: number, product: Partial<ProductFormData>): Promise<Product> => {
    const { data } = await api.put(`/products/${id}`, product);
    return data;
  },

  deleteProduct: async (id: number): Promise<Product & { isDeleted: boolean; deletedOn: string }> => {
    const { data } = await api.delete(`/products/${id}`);
    return data;
  },

  getCategories: async (): Promise<string[]> => {
    const { data } = await api.get('/products/category-list');
    return data;
  },

  getProductsByCategory: async (category: string, params?: Partial<ProductQueryParams>): Promise<ProductsResponse> => {
    const { data } = await api.get(`/products/category/${category}`, { params });
    return data;
  },
};
