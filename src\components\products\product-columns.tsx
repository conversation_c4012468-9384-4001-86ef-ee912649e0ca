"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import { DataTableRowActions } from "@/components/table/data-table-row-actions";
import { Product } from "@/lib/types";
import { Eye, Edit, Trash2 } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useDeleteProduct } from "@/lib/hooks/use-products";

interface ProductColumnsProps {
  onEdit: (product: Product) => void;
}

export const createProductColumns = ({ onEdit }: ProductColumnsProps): ColumnDef<Product>[] => [
  {
    accessorKey: "thumbnail",
    header: "Image",
    cell: ({ row }) => (
      <div className="w-12 h-12 md:w-16 md:h-16 relative">
        <Image
          src={row.getValue("thumbnail")}
          alt={row.getValue("title")}
          fill
          className="object-cover rounded-md"
          sizes="(max-width: 768px) 48px, 64px"
        />
      </div>
    ),
    enableSorting: false,
  },
  {
    accessorKey: "title",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Title" />
    ),
    cell: ({ row }) => {
      const router = useRouter();
      return (
        <div className="max-w-[150px] md:max-w-[200px]">
          <Button
            variant="link"
            className="p-0 h-auto font-medium text-left justify-start text-xs md:text-sm"
            onClick={() => router.push(`/products/${row.original.id}`)}
          >
            <span className="truncate">{row.getValue("title")}</span>
          </Button>
        </div>
      );
    },
  },
  {
    accessorKey: "category",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Category" />
    ),
    cell: ({ row }) => (
      <Badge variant="secondary" className="capitalize">
        {row.getValue("category")}
      </Badge>
    ),
  },
  {
    accessorKey: "brand",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Brand" className="hidden md:table-cell" />
    ),
    cell: ({ row }) => (
      <div className="hidden md:block">
        {row.getValue("brand")}
      </div>
    ),
  },
  {
    accessorKey: "price",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Price" />
    ),
    cell: ({ row }) => {
      const price = parseFloat(row.getValue("price"));
      const formatted = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(price);
      return <div className="font-medium">{formatted}</div>;
    },
  },
  {
    accessorKey: "rating",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Rating" className="hidden lg:table-cell" />
    ),
    cell: ({ row }) => {
      const rating = parseFloat(row.getValue("rating"));
      return (
        <div className="hidden lg:flex items-center">
          <span className="font-medium">{rating.toFixed(1)}</span>
          <span className="text-yellow-500 ml-1">★</span>
        </div>
      );
    },
  },
  {
    accessorKey: "stock",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Stock" />
    ),
    cell: ({ row }) => {
      const stock = parseInt(row.getValue("stock"));
      return (
        <Badge variant={stock > 10 ? "default" : stock > 0 ? "secondary" : "destructive"}>
          {stock}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const router = useRouter();
      const deleteProduct = useDeleteProduct();

      return (
        <div className="flex items-center gap-1 md:gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/products/${row.original.id}`)}
            className="h-8 w-8 p-0 md:h-9 md:w-9"
          >
            <Eye className="h-3 w-3 md:h-4 md:w-4" />
            <span className="sr-only">View</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(row.original)}
            className="h-8 w-8 p-0 md:h-9 md:w-9"
          >
            <Edit className="h-3 w-3 md:h-4 md:w-4" />
            <span className="sr-only">Edit</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => deleteProduct.mutate(row.original.id)}
            disabled={deleteProduct.isPending}
            className="h-8 w-8 p-0 md:h-9 md:w-9"
          >
            <Trash2 className="h-3 w-3 md:h-4 md:w-4" />
            <span className="sr-only">Delete</span>
          </Button>
        </div>
      );
    },
  },
];
