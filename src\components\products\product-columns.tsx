"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import { DataTableRowActions } from "@/components/table/data-table-row-actions";
import { Product } from "@/lib/types";
import { Eye, Edit, Trash2 } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useDeleteProduct } from "@/lib/hooks/use-products";

interface ProductColumnsProps {
  onEdit: (product: Product) => void;
}

export const createProductColumns = ({ onEdit }: ProductColumnsProps): ColumnDef<Product>[] => [
  {
    accessorKey: "thumbnail",
    header: "Image",
    cell: ({ row }) => (
      <div className="w-16 h-16 relative">
        <Image
          src={row.getValue("thumbnail")}
          alt={row.getValue("title")}
          fill
          className="object-cover rounded-md"
          sizes="64px"
        />
      </div>
    ),
    enableSorting: false,
  },
  {
    accessorKey: "title",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Title" />
    ),
    cell: ({ row }) => {
      const router = useRouter();
      return (
        <div className="max-w-[200px]">
          <Button
            variant="link"
            className="p-0 h-auto font-medium text-left justify-start"
            onClick={() => router.push(`/products/${row.original.id}`)}
          >
            {row.getValue("title")}
          </Button>
        </div>
      );
    },
  },
  {
    accessorKey: "category",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Category" />
    ),
    cell: ({ row }) => (
      <Badge variant="secondary" className="capitalize">
        {row.getValue("category")}
      </Badge>
    ),
  },
  {
    accessorKey: "brand",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Brand" />
    ),
  },
  {
    accessorKey: "price",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Price" />
    ),
    cell: ({ row }) => {
      const price = parseFloat(row.getValue("price"));
      const formatted = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(price);
      return <div className="font-medium">{formatted}</div>;
    },
  },
  {
    accessorKey: "rating",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Rating" />
    ),
    cell: ({ row }) => {
      const rating = parseFloat(row.getValue("rating"));
      return (
        <div className="flex items-center">
          <span className="font-medium">{rating.toFixed(1)}</span>
          <span className="text-yellow-500 ml-1">★</span>
        </div>
      );
    },
  },
  {
    accessorKey: "stock",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Stock" />
    ),
    cell: ({ row }) => {
      const stock = parseInt(row.getValue("stock"));
      return (
        <Badge variant={stock > 10 ? "default" : stock > 0 ? "secondary" : "destructive"}>
          {stock}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const router = useRouter();
      const deleteProduct = useDeleteProduct();

      return (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/products/${row.original.id}`)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(row.original)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => deleteProduct.mutate(row.original.id)}
            disabled={deleteProduct.isPending}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];
