"use client"

import { type Table } from "@tanstack/react-table"
import { X } from "lucide-react"

import { Button } from "../ui/button"
import { SearchInput } from "../ui/search-input"
import { DataTableViewOptions } from "./data-table-view-options"

interface FilterOption {
  columnId: string;
  title: string;
  options: {
    label: string;
    value: string;
    icon?: React.ComponentType<{ className?: string }>;
  }[];
}

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  filters?: FilterOption[];
  searchTerm?: string;
  onSearchChange?: (searchTerm: string) => void;
}

export function DataTableToolbar<TData>({
  table,
  filters = [],
  searchTerm,
  onSearchChange,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const hasSearchTerm = searchTerm && searchTerm.length > 0

  const handleClearSearch = () => {
    if (onSearchChange) {
      onSearchChange('');
    }
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        {onSearchChange ? (
          <SearchInput
            placeholder="Search products..."
            value={searchTerm ?? ""}
            onChange={(event) => onSearchChange(event.target.value)}
            onClear={handleClearSearch}
            className="h-8 w-[150px] lg:w-[250px]"
          />
        ) : (
          <SearchInput
            placeholder="Filter products..."
            value={(table.getColumn("title")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("title")?.setFilterValue(event.target.value)
            }
            className="h-8 w-[150px] lg:w-[250px]"
          />
        )}

        {(isFiltered || hasSearchTerm) && (
          <Button
            variant="ghost"
            onClick={() => {
              table.resetColumnFilters();
              if (hasSearchTerm && onSearchChange) {
                onSearchChange('');
              }
            }}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X className="ml-1 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
