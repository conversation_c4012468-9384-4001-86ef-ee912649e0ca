"use client";

import { useState, useEffect } from "react";
import { ProductsTable } from "@/components/products/products-table";
import { createProductColumns } from "@/components/products/product-columns";
import { ProductForm } from "@/components/products/product-form";
import { useProducts, useSearchProducts } from "@/lib/hooks/use-products";
import { useDebouncedSearch } from "@/lib/hooks/use-debounced-search";
import { Product } from "@/lib/types";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import Navbar from "@/components/navbar";

export default function ProductsPage() {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const [sorting, setSorting] = useState<Array<{ id: string; desc: boolean }>>([]);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  // Search functionality with debouncing
  const { searchTerm, debouncedSearchTerm, setSearchTerm } = useDebouncedSearch('', 300);

  // Reset pagination when search term changes
  useEffect(() => {
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  }, [debouncedSearchTerm]);

  const queryParams = {
    limit: pagination.pageSize,
    skip: pagination.pageIndex * pagination.pageSize,
    sortBy: sorting[0]?.id,
    order: (sorting[0]?.desc ? 'desc' : 'asc') as 'asc' | 'desc',
  };

  // Use search API when there's a search term, otherwise use regular products API
  const { data: searchData, isLoading: isSearchLoading, error: searchError } = useSearchProducts(
    debouncedSearchTerm,
    queryParams
  );

  const { data: productsData, isLoading: isProductsLoading, error: productsError } = useProducts(
    queryParams
  );

  // Determine which data to use based on search state
  const data = debouncedSearchTerm ? searchData : productsData;
  const isLoading = debouncedSearchTerm ? isSearchLoading : isProductsLoading;
  const error = debouncedSearchTerm ? searchError : productsError;



  useEffect(() => {
    document.title = "All Products – MyShop";
  }, []);

  const handleEdit = (product: Product) => {
    setEditingProduct(product);
  };

  const columns = createProductColumns({ onEdit: handleEdit });

  if (error) {
    return (
      <div className="min-h-screen overflow-auto">
        <Navbar />
        <div className="container mx-auto py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600">Error loading products</h1>
            <p className="text-gray-600 mt-2">Please try again later.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Navbar />
      <div className="container mx-auto px-4 py-4 md:py-8">
        <div className="flex flex-col gap-4 mb-6 sm:flex-row sm:justify-between sm:items-center">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Products</h1>
            <p className="text-gray-600 mt-1 text-sm md:text-base">
              Manage your product inventory
            </p>
          </div>
          <Button
            onClick={() => setShowAddForm(true)}
            className="w-full sm:w-auto"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>

        <ProductsTable
          columns={columns}
          data={data?.products || []}
          pageCount={data?.total ? Math.ceil(data.total / pagination.pageSize) : 0}
          pagination={pagination}
          onPaginationChange={setPagination}
          sorting={sorting}
          onSortingChange={setSorting}
          isLoading={isLoading}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
        />

        <ProductForm
          product={editingProduct}
          open={showAddForm || !!editingProduct}
          onOpenChange={(open) => {
            if (!open) {
              setShowAddForm(false);
              setEditingProduct(null);
            }
          }}
        />
      </div>
    </div>
  );
}
