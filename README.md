# Frontend Task: Product Table with API Integration, Zod Validation & Navigation

**Completed by: UDESHYA SHARMA**

## Implementation Status: ✅ COMPLETED

This repository contains a fully functional product management application built with Next.js, featuring:

- ✅ **Complete CRUD Operations** - Add, Edit, Delete products
- ✅ **Advanced Pagination** - Sliding window pagination (5 pages visible)
- ✅ **Server-side Sorting** - Sort by title, price, rating, etc.
- ✅ **Search with Debouncing** - Real-time search functionality with 300ms debouncing
- ✅ **Form Validation** - Zod schema validation with React Hook Form
- ✅ **Image Upload** - Product image upload with preview
- ✅ **Dynamic Routing** - Product detail pages with dynamic titles
- ✅ **TanStack Query** - Efficient data fetching and caching
- ✅ **Responsive UI** - Modern, clean interface with Tailwind CSS
- ✅ **Error Handling** - Comprehensive error handling and user feedback

## 🚀 Key Features Implemented

### 1. **Product Management System**
- **Create**: Add new products with comprehensive form validation
- **Read**: View products in a data table with pagination and sorting
- **Update**: Edit existing products with pre-filled data
- **Delete**: Remove products with confirmation dialogs
- **View Details**: Navigate to individual product pages with dynamic titles

### 2. **Advanced Search & Filtering**
- **Debounced Search**: Real-time search with 300ms debouncing to optimize API calls
- **Server-side Search**: Utilizes DummyJSON search API for efficient querying
- **Category Filtering**: Filter products by categories
- **Multi-field Search**: Search across product titles, descriptions, and brands

### 3. **Data Table Features**
- **Server-side Pagination**: Efficient handling of large datasets
- **Sortable Columns**: Sort by title, price, rating, stock, category, and brand
- **Responsive Design**: Optimized for desktop and mobile devices
- **Loading States**: Skeleton loaders and loading indicators

### 4. **Form Validation & User Experience**
- **Zod Schema Validation**: Type-safe form validation with detailed error messages
- **React Hook Form**: Efficient form handling with minimal re-renders
- **Image Upload**: Product image upload with preview functionality
- **Toast Notifications**: User feedback for all CRUD operations
- **Error Boundaries**: Comprehensive error handling throughout the application

## 🛠 Technical Stack

- **Frontend Framework**: Next.js 15.2.3 with TypeScript
- **State Management**: TanStack Query v5 for server state
- **Form Handling**: React Hook Form with Zod validation
- **UI Components**: Radix UI primitives with Tailwind CSS
- **Data Table**: TanStack Table v8 for advanced table functionality
- **API Integration**: Axios for HTTP requests to DummyJSON API
- **Image Optimization**: Next.js Image component
- **Styling**: Tailwind CSS with custom component library
- **Icons**: Lucide React for consistent iconography
- **Notifications**: React Toastify for user feedback

## 🎯 Challenges Handled

### 1. **Search Functionality Issue**
**Problem**: The original search input was using client-side filtering which didn't work properly with server-side pagination.

**Solution**:
- Implemented proper server-side search using DummyJSON's search API
- Added debouncing (300ms) to optimize API calls and improve performance
- Integrated search state with pagination and sorting for seamless user experience

### 2. **Form State Management**
**Problem**: Complex form validation and state management for product creation/editing.

**Solution**:
- Used React Hook Form with Zod schema validation for type-safe forms
- Implemented proper error handling and user feedback
- Added image upload functionality with preview

### 3. **Performance Optimization**
**Problem**: Efficient data fetching and caching for large product datasets.

**Solution**:
- Leveraged TanStack Query for intelligent caching and background updates
- Implemented server-side pagination to handle large datasets
- Used optimistic updates for better user experience

### 4. **Responsive Design**
**Problem**: Creating a responsive data table that works on all device sizes.

**Solution**:
- Implemented responsive design patterns with Tailwind CSS
- Used proper breakpoints and mobile-first approach
- Optimized table layout for mobile devices

### 6. Dynamic Page Titles

- Use the product name (or relevant context) as the document `<title>` for each page.
  - Example:
    - `/products`: “All Products – MyShop”
    - `/products/12`: “iPhone 9 – MyShop”

---

## Form Validation with Zod

Use `zod` and `@hookform/resolvers/zod` for schema validation. Example schema:

```typescript
import { z } from 'zod';

export const productSchema = z.object({
  username: z.string().min(1, { message: 'Username is required' }),
  password: z.string().min(1, { message: 'Password is required' })
});
```

Integrate this schema with `react-hook-form` to validate form inputs and display appropriate error messages.

---

## Evaluation Criteria

* **Functionality** : Correct implementation of data fetching, pagination, sorting, CRUD operations, and routing.
* **Code Quality** : Clean, readable, and maintainable code.
* **User Experience** : Intuitive and responsive UI/UX.
* **Validation** : Effective use of Zod for form validation with appropriate error messages.
* **State Management** : Efficient use of TanStack Query for data fetching and state management.
* **Error Handling** : Proper handling of API errors and edge cases.

---

## Submission Guidelines

1. **Fork the Repository.**
2. **Make Your Changes**
3. **Push Changes and Create a Pull Request** :

* Ensure the base repository is `QarbonaAI/fe-task` and the base branch is `main`.
* Set the title of the pull request to your  **full name** .
* Provide a brief description of the changes you've made.

---

## 🚀 Getting Started

### Prerequisites
- Node.js (>= 18.0.0)
- npm, yarn, or pnpm

### Installation
1. Clone the repository
2. Install dependencies: `npm install`
3. Run the development server: `npm run dev`
4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## 📝 Implementation Notes

This project demonstrates modern React development practices including:
- Server-side rendering with Next.js
- Type-safe development with TypeScript
- Efficient state management with TanStack Query
- Form validation with Zod schemas
- Responsive design with Tailwind CSS
- Proper error handling and user feedback

The search functionality has been specifically enhanced to provide a smooth user experience with debouncing and server-side filtering, addressing the original issue where search was not working properly.

---

## 📋 Repository Setup Instructions

This is a **private repository** created for the QarbonaAI frontend task following the submission guidelines.

### Submission Guidelines Followed:
1. ✅ **Cloned the base repository** from: https://github.com/QarbonaAI/fe-task
2. ✅ **Created a private GitHub repository** under personal account
3. ✅ **Added required collaborators** with GitHub usernames:
   - `aditansh`
   - `AchintyaQarbona`
   - `siddharth1825`
   - `abhitrueprogrammer`
4. ✅ **Added full name** in README (placeholder provided for completion)
5. ✅ **Repository remains private** to ensure fairness and prevent plagiarism

### Important Notes:
- This repository will **never be made public** as per submission guidelines
- All required collaborators have been added with appropriate access permissions
- The repository link will be submitted as part of the application process
