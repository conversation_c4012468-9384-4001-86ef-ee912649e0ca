import { z } from 'zod';

export const productFormSchema = z.object({
  title: z.string().min(1, { message: 'Title is required' }).max(100, { message: 'Title must be less than 100 characters' }),
  description: z.string().min(1, { message: 'Description is required' }).max(500, { message: 'Description must be less than 500 characters' }),
  category: z.string().min(1, { message: 'Category is required' }),
  price: z.number().min(0.01, { message: 'Price must be greater than 0' }).max(999999, { message: 'Price must be less than 1,000,000' }),
  discountPercentage: z.number().min(0).max(100).optional(),
  rating: z.number().min(0).max(5).optional(),
  stock: z.number().int().min(0, { message: 'Stock must be 0 or greater' }),
  brand: z.string().min(1, { message: 'Brand is required' }).max(50, { message: 'Brand must be less than 50 characters' }),
  tags: z.array(z.string()).optional(),
  thumbnail: z.string().optional(),
});

export const productQuerySchema = z.object({
  limit: z.number().int().min(1).max(100).default(30),
  skip: z.number().int().min(0).default(0),
  sortBy: z.string().optional(),
  order: z.enum(['asc', 'desc']).optional(),
  search: z.string().optional(),
  category: z.string().optional(),
});

export type ProductFormData = z.infer<typeof productFormSchema>;
export type ProductQueryParams = z.infer<typeof productQuerySchema>;
