"use client";

import { Product } from "@/lib/types";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Eye, Edit, Trash2 } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useDeleteProduct } from "@/lib/hooks/use-products";

interface MobileProductCardProps {
  product: Product;
  onEdit: (product: Product) => void;
}

export function MobileProductCard({ product, onEdit }: MobileProductCardProps) {
  const router = useRouter();
  const deleteProduct = useDeleteProduct();

  const handleView = () => {
    router.push(`/products/${product.id}`);
  };

  const handleEdit = () => {
    onEdit(product);
  };

  const handleDelete = () => {
    deleteProduct.mutate(product.id);
  };

  const price = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(product.price);

  return (
    <Card className="w-full hover:shadow-md transition-shadow">
      <CardContent className="p-3">
        <div className="flex gap-3">
          {/* Product Image */}
          <div className="w-14 h-14 relative flex-shrink-0">
            <Image
              src={product.thumbnail}
              alt={product.title}
              fill
              className="object-cover rounded-md"
              sizes="56px"
            />
          </div>

          {/* Product Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <div className="flex-1 min-w-0">
                <Button
                  variant="link"
                  className="p-0 h-auto font-medium text-left justify-start text-sm leading-tight"
                  onClick={handleView}
                >
                  <span className="truncate block">{product.title}</span>
                </Button>
                <div className="flex items-center gap-2 mt-1 flex-wrap">
                  <Badge variant="secondary" className="text-xs px-2 py-0.5">
                    {product.category}
                  </Badge>
                  <span className="text-sm font-semibold text-green-600">{price}</span>
                </div>
                <div className="flex items-center gap-3 mt-1.5 text-xs text-muted-foreground">
                  <span className="flex items-center">
                    <span className="font-medium">Stock:</span>
                    <span className={`ml-1 ${product.stock > 10 ? 'text-green-600' : product.stock > 0 ? 'text-yellow-600' : 'text-red-600'}`}>
                      {product.stock}
                    </span>
                  </span>
                  <span className="flex items-center">
                    <span className="font-medium">{product.rating.toFixed(1)}</span>
                    <span className="text-yellow-500 ml-0.5">★</span>
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleView}
                  className="h-7 w-7 p-0"
                >
                  <Eye className="h-3 w-3" />
                  <span className="sr-only">View</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleEdit}
                  className="h-7 w-7 p-0"
                >
                  <Edit className="h-3 w-3" />
                  <span className="sr-only">Edit</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDelete}
                  disabled={deleteProduct.isPending}
                  className="h-7 w-7 p-0"
                >
                  <Trash2 className="h-3 w-3" />
                  <span className="sr-only">Delete</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
